<?php

namespace common\service\job;

use common\base\models\BaseAdmin;
use common\base\models\BaseCompany;
use common\base\models\BaseCompanyMemberInfo;
use common\base\models\BaseCompanyPackageChangeLog;
use common\base\models\BaseCompanyPackageConfig;
use common\base\models\BaseCompanyPackageSystemConfig;
use common\base\models\BaseJob;
use common\base\models\BaseJobHandleLog;
use common\base\models\BaseMember;
use common\base\models\BaseUser;
use common\helpers\IpHelper;
use common\helpers\TimeHelper;
use common\service\companyPackage\CompanyPackageApplication;
use common\service\package\CompanyService;
use yii\base\Exception;

/**
 * 整个职位模块的基础服务
 */
class BaseService
{

    public $operatorId   = '';
    public $operatorType = '';
    public $operatorName = '';

    public $companyId       = '';
    public $companyMemberId = '';

    // 这里是前端传过来的job的data数据
    public $jobData;

    public $jobId;

    public $saveType;

    /**
     * @var BaseJob
     */
    public $jobModel;

    /**
     * @var BaseCompany
     */
    public $companyModel;

    /**
     * @var BaseAdmin
     */
    public $adminModel;

    /**
     * @var BaseMember
     */
    public $companyMemberModel;

    /**
     * @var BaseCompanyPackageConfig
     */
    public $companyPackageConfigModel;

    const ACTION_TYPE_ADD           = 1;
    const ACTION_TYPE_EDIT          = 2;
    const ACTION_TYPE_AUDIT         = 3;
    const ACTION_TYPE_REFRESH       = 4;
    const ACTION_TYPE_RELEASE_AGAIN = 5;

    const OPERATOR_TYPE_ADMIN   = BaseUser::TYPE_ADMIN;
    const OPERATOR_TYPE_COMPANY = BaseUser::TYPE_MEMBER;

    const OPERATOR_HANDLE_TYPE_PERSON  = 1;
    const OPERATOR_HANDLE_TYPE_COMPANY = 2;

    // 暂存(保存)
    const SAVE_TYPE_STAGING = 1;
    // 发布职位(提交审核)
    const SAVE_TYPE_AUDIT = 2;

    public $isNew = 0;

    const CREATE_TYPE_EDIT = 1;
    const CREATE_TYPE_ADD  = 2;

    /**
     * @var 操作的类型
     */
    public $actionType;
    /**
     * @var mixed
     */
    public $operatorUserName;
    public $create_type;

    protected function setCompany()
    {
        if (!$this->companyModel) {
            $this->companyModel       = BaseCompany::findOne($this->companyId);
            $this->companyMemberModel = BaseMember::findOne($this->companyModel->member_id);
            $this->companyMemberId    = $this->companyModel->member_id;
        }
    }

    /**
     * @throws Exception
     */
    protected function setJob($id)
    {
        $this->jobModel = BaseJob::findOne($id);
        if (!$this->jobModel) {
            throw new Exception('职位不存在');
        }
        $this->jobId = $id;
    }

    /**
     * 设置操作人(如果是企业操作,这里就是member_id)
     * @return $this
     */
    public function setOperator($operatorId, $operatorType)
    {
        $this->operatorId   = $operatorId;
        $this->operatorType = $operatorType;

        if ($this->operatorType == self::OPERATOR_TYPE_COMPANY) {
            // 企业的信息
            $companyMemberInfo      = BaseCompanyMemberInfo::findOne(['member_id' => $operatorId]);
            $company                = BaseCompany::findOne($companyMemberInfo['company_id']);
            $this->companyId        = $company->id;
            $this->operatorUserName = $companyMemberInfo['contact'];
            $this->create_type      = 1;
            $this->setCompany();
        }

        if ($this->operatorType == self::OPERATOR_TYPE_ADMIN) {
            // 运营后台添加的
            $this->operatorUserName = BaseAdmin::findOneVal(['id' => $operatorId], 'name');
            $this->create_type      = 2;
        }

        return $this;
    }

    /**
     * 检查会员套餐
     * @throws Exception
     */
    protected function checkMemberPackage()
    {
        if ($this->operatorType == self::OPERATOR_TYPE_COMPANY) {
            if ($this->jobModel) {
                if ($this->jobModel->company_id != $this->companyId) {
                    throw new Exception('职位不属于当前企业');
                }
            }

            // 企业自己操作的
            $this->companyPackageConfigModel = BaseCompanyPackageConfig::findOne([
                'company_id' => $this->companyModel->id,
                'status'     => BaseCompanyPackageConfig::STATUS_ACTIVE,
            ]);

            if (!$this->companyPackageConfigModel) {
                throw new Exception('套餐不存在');
            }

            if ($this->companyPackageConfigModel->effect_time > CUR_DATETIME) {
                throw new Exception('套餐还没生效');
            }

            // if ($this->companyPackageConfigModel->expire_time < CUR_DATETIME && $this->actionType != self::ACTION_TYPE_EDIT) {
            //     throw new Exception('套餐已过期');
            // }

            if ($this->actionType == self::ACTION_TYPE_ADD) {
                if ($this->saveType == self::SAVE_TYPE_AUDIT && $this->jobModel->is_consume_release != BaseJob::IS_CONSUME_RELEASE_YES) {
                    $this->isNew = 1;
                    if ($this->companyPackageConfigModel->job_amount <= 0) {
                        throw new Exception('职位数量已达上限');
                    }
                }
            }

            if ($this->actionType == self::ACTION_TYPE_RELEASE_AGAIN) {
                if ($this->companyPackageConfigModel->job_amount <= 0) {
                    throw new Exception('职位数量已达上限');
                }
                // 再次发布职位
                if (TimeHelper::reduceDates(CUR_DATETIME,
                        $this->jobModel->release_time) < $this->companyPackageConfigModel->job_release_interval_day) {
                    throw new Exception('职位距离上次提交发布还未满：' . $this->companyPackageConfigModel->job_release_interval_day . '天');
                }
            }

            if ($this->actionType == self::ACTION_TYPE_REFRESH) {
                // 检查次数
                if ($this->companyPackageConfigModel->refresh_amount <= 0) {
                    throw new Exception('刷新次数已达上限');
                }
                // 刷新职位
                if (TimeHelper::reduceDates(CUR_DATETIME,
                        $this->jobModel->refresh_time) < $this->companyPackageConfigModel->job_refresh_interval_day) {
                    throw new Exception('职位距离上次刷新还未满：' . $this->companyPackageConfigModel->job_refresh_interval_day . '天');
                }
            }
        }
    }

    /**
     * 添加日志
     */
    protected function log()
    {
        $data = [
            'company_id'   => $this->companyId,
            'job_id'       => $this->jobModel->id,
            'ip'           => IpHelper::getIpInt(),
            'handler_type' => $this->operatorType,
            'handler_id'   => $this->operatorId,
            'handler_name' => $this->operatorName,
        ];
        switch ($this->actionType) {
            case self::ACTION_TYPE_ADD:
                break;
            case self::ACTION_TYPE_EDIT:
                break;
            case self::ACTION_TYPE_AUDIT:
                break;
            default:
                break;
        }
    }

    protected function beforeRun()
    {
        $this->checkMemberPackage();
    }

    protected function afterRun()
    {
        // 写日志
        $this->handelMemberPackage();
    }

    /**
     * 消费
     * @return void
     * @throws Exception
     */
    protected function consumptionRelease()
    {
        $oldAmount = $this->companyPackageConfigModel->job_amount;
        // 扣除发布次数
        $this->companyPackageConfigModel->updateCounters([
            'job_amount' => -1,
        ]);

        $type              = BaseCompanyPackageChangeLog::TYPE_JOB_RELEASE;
        $packageConfigName = BaseCompanyPackageSystemConfig::PACKAGE_CONFIG_NAME;
        $packageSurplus    = [
            $packageConfigName['job_amount']                  => $this->companyPackageConfigModel->job_amount,
            $packageConfigName['announcement_amount']         => $this->companyPackageConfigModel->announcement_amount,
            $packageConfigName['job_refresh_amount']          => $this->companyPackageConfigModel->job_refresh_amount,
            $packageConfigName['announcement_refresh_amount'] => $this->companyPackageConfigModel->announcement_refresh_amount,
        ];
        $packageSurplus    = json_encode($packageSurplus);
        $data              = [
            'type'            => $type,
            'identify'        => BaseCompanyPackageChangeLog::IDENTIFY_REDUCE,
            'change_amount'   => 1,
            'surplus'         => $this->companyPackageConfigModel->job_amount,
            'package_surplus' => $packageSurplus,
            'member_id'       => $this->companyMemberModel->id,
            'member_name'     => $this->companyMemberModel->username ?: '',
            'company_id'      => $this->companyModel->id ?: '',
            'company_name'    => $this->companyModel->full_name ?: '',
            'handle_before'   => $oldAmount . '',
            'handle_after'    => $this->companyPackageConfigModel->job_amount . '',
            'handler_type'    => $this->operatorType,
            'handler_id'      => $this->operatorId,
            'handler'         => $this->operatorUserName,
            'content'         => BaseCompanyPackageChangeLog::TYPE_NAME[$type],
            'remark'          => '',
        ];

        BaseCompanyPackageChangeLog::createCompanyPackageChangeLog($data);

        $this->jobModel->is_consume_release = 1;
    }

    protected function consumptionRefresh()
    {
        $oldAmount = $this->companyPackageConfigModel->job_refresh_amount;
        // 扣除发布次数
        $this->companyPackageConfigModel->updateCounters([
            'announcement_refresh_amount' => -1,
        ]);
        $type              = BaseCompanyPackageChangeLog::TYPE_ANNOUNCEMENT_REFRESH;
        $packageConfigName = BaseCompanyPackageSystemConfig::PACKAGE_CONFIG_NAME;
        $packageSurplus    = [
            $packageConfigName['job_amount']                  => $this->companyPackageConfigModel->job_amount,
            $packageConfigName['announcement_amount']         => $this->companyPackageConfigModel->announcement_amount,
            $packageConfigName['job_refresh_amount']          => $this->companyPackageConfigModel->job_refresh_amount,
            $packageConfigName['announcement_refresh_amount'] => $this->companyPackageConfigModel->announcement_refresh_amount,
        ];
        $packageSurplus    = json_encode($packageSurplus);
        $data              = [
            'type'            => $type,
            'identify'        => BaseCompanyPackageChangeLog::IDENTIFY_REDUCE,
            'change_amount'   => 1,
            'surplus'         => $this->companyPackageConfigModel->job_refresh_amount,
            'package_surplus' => $packageSurplus,
            'member_id'       => $this->companyMemberModel->id,
            'member_name'     => $this->companyMemberModel->username ?: '',
            'company_id'      => $this->companyModel->id ?: '',
            'company_name'    => $this->companyModel->full_name ?: '',
            'handle_before'   => $oldAmount . '',
            'handle_after'    => $this->companyPackageConfigModel->job_refresh_amount . '',
            'handler_type'    => $this->operatorType,
            'handler_id'      => $this->operatorId,
            'handler'         => $this->operatorUserName,
            'content'         => BaseCompanyPackageChangeLog::TYPE_NAME[$type],
            'remark'          => '',
        ];

        BaseCompanyPackageChangeLog::createCompanyPackageChangeLog($data);

        $this->jobModel->is_consume_refresh = BaseJob::IS_CONSUME_RELEASE_YES;
        $this->jobModel->save();
    }

    /**
     * @throws Exception
     * @throws \yii\db\Exception
     */
    protected function handelMemberPackage()
    {
        if ($this->operatorType == self::OPERATOR_TYPE_COMPANY) {
            // 在这里处理会员套餐的消费
            switch ($this->actionType) {
                case self::ACTION_TYPE_ADD:
                    // 新增,这里其实有编辑和新增的区别,如果是新增或者没有任何审核通过历史
                    if ($this->saveType == self::SAVE_TYPE_AUDIT && $this->jobModel->is_consume_release != BaseJob::IS_CONSUME_RELEASE_YES) {
                        // 发布职位
                        //$this->consumptionRelease();
                        $companyPackageApplication = new CompanyPackageApplication();
                        $handleType                = BaseCompanyPackageChangeLog::HANDLE_TYPE_JOB_RELEASE;
                        $remark                    = BaseCompanyPackageChangeLog::HANDLER_TYPE_NAME[$handleType];
                        $companyPackageApplication->jobRelease($this->companyModel->id, 1, $remark);
                    }

                    break;
                case self::ACTION_TYPE_RELEASE_AGAIN:
                    // 发布职位
                    $companyPackageApplication = new CompanyPackageApplication();
                    $handleType                = BaseCompanyPackageChangeLog::HANDLE_TYPE_JOB_RELEASE;
                    $remark                    = BaseCompanyPackageChangeLog::HANDLER_TYPE_NAME[$handleType];
                    $companyPackageApplication->jobRelease($this->companyModel->id, 1, $remark);
                    break;
                case self::ACTION_TYPE_REFRESH:
                    $companyPackageApplication = new CompanyPackageApplication();
                    $handleType                = BaseCompanyPackageChangeLog::HANDLE_TYPE_JOB_REFRESH;
                    $remark                    = BaseCompanyPackageChangeLog::HANDLER_TYPE_NAME[$handleType];
                    $companyPackageApplication->jobRefresh($this->companyModel->id, 1, $remark);
                    break;
            }
        }
    }
}
