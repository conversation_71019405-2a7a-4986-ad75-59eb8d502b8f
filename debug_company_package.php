<?php
/**
 * 调试企业套餐过期问题
 * 用于检查企业ID 111182 的套餐配置信息
 */

// 设置错误报告
error_reporting(E_ALL);
ini_set('display_errors', 1);

// 引入Yii框架
defined('YII_DEBUG') or define('YII_DEBUG', true);
defined('YII_ENV') or define('YII_ENV', 'dev');

require __DIR__ . '/vendor/autoload.php';
require __DIR__ . '/vendor/yiisoft/yii2/Yii.php';
require __DIR__ . '/common/config/bootstrap.php';

// 加载配置
$config = yii\helpers\ArrayHelper::merge(
    require __DIR__ . '/common/config/main.php',
    require __DIR__ . '/common/config/main-local.php',
    require __DIR__ . '/console/config/main.php'
);
$application = new yii\console\Application($config);

use common\base\models\BaseCompanyPackageConfig;
use common\base\models\BaseCompany;

// 从错误日志中获取的企业ID
$companyId = 111182;

echo "=== 企业套餐过期问题调试 ===\n";
echo "企业ID: {$companyId}\n";
echo "当前时间: " . CUR_DATETIME . "\n";
echo "当前时间戳: " . CUR_TIMESTAMP . "\n\n";

// 1. 查询企业基本信息
echo "1. 企业基本信息:\n";
$company = BaseCompany::findOne($companyId);
if ($company) {
    echo "   企业名称: {$company->full_name}\n";
    echo "   企业状态: {$company->status}\n";
    echo "   套餐类型: {$company->package_type}\n";
    echo "   是否合作单位: {$company->is_cooperation}\n";
    echo "   创建时间: {$company->add_time}\n";
} else {
    echo "   企业不存在!\n";
    exit(1);
}

echo "\n";

// 2. 查询企业套餐配置
echo "2. 企业套餐配置:\n";
$packageConfig = BaseCompanyPackageConfig::findOne([
    'company_id' => $companyId,
    'status' => BaseCompanyPackageConfig::STATUS_ACTIVE
]);

if ($packageConfig) {
    echo "   套餐ID: {$packageConfig->id}\n";
    echo "   套餐名称: {$packageConfig->name}\n";
    echo "   套餐代码: {$packageConfig->code}\n";
    echo "   状态: {$packageConfig->status}\n";
    echo "   生效时间: {$packageConfig->effect_time}\n";
    echo "   过期时间: {$packageConfig->expire_time}\n";
    echo "   职位发布数量: {$packageConfig->job_amount}\n";
    echo "   公告发布数量: {$packageConfig->announcement_amount}\n";
    echo "   职位刷新次数: {$packageConfig->job_refresh_amount}\n";
    echo "   公告刷新次数: {$packageConfig->announcement_refresh_amount}\n";
    
    // 时间比较
    echo "\n   时间比较:\n";
    $effectTime = strtotime($packageConfig->effect_time);
    $expireTime = strtotime($packageConfig->expire_time);
    $currentTime = strtotime(CUR_DATETIME);
    
    echo "   生效时间戳: {$effectTime} (" . date('Y-m-d H:i:s', $effectTime) . ")\n";
    echo "   过期时间戳: {$expireTime} (" . date('Y-m-d H:i:s', $expireTime) . ")\n";
    echo "   当前时间戳: {$currentTime} (" . date('Y-m-d H:i:s', $currentTime) . ")\n";
    
    echo "   套餐是否已生效: " . ($effectTime <= $currentTime ? "是" : "否") . "\n";
    echo "   套餐是否已过期: " . ($expireTime < $currentTime ? "是" : "否") . "\n";
    
    if ($expireTime < $currentTime) {
        $expiredDays = floor(($currentTime - $expireTime) / 86400);
        echo "   已过期天数: {$expiredDays} 天\n";
    } else {
        $remainingDays = floor(($expireTime - $currentTime) / 86400);
        echo "   剩余天数: {$remainingDays} 天\n";
    }
    
} else {
    echo "   未找到有效的套餐配置!\n";
    
    // 查询所有套餐配置（包括无效的）
    echo "\n   查询所有套餐配置:\n";
    $allConfigs = BaseCompanyPackageConfig::find()
        ->where(['company_id' => $companyId])
        ->orderBy('add_time DESC')
        ->all();
    
    if ($allConfigs) {
        foreach ($allConfigs as $config) {
            echo "   - ID: {$config->id}, 状态: {$config->status}, 过期时间: {$config->expire_time}\n";
        }
    } else {
        echo "   该企业没有任何套餐配置记录!\n";
    }
}

echo "\n";

// 3. 检查套餐过期逻辑
echo "3. 套餐过期检查逻辑分析:\n";
if ($packageConfig) {
    echo "   baseCheckMemberPackage() 方法检查:\n";
    
    // 模拟 baseCheckMemberPackage 的检查逻辑
    if ($packageConfig->effect_time > CUR_DATETIME) {
        echo "   ❌ 套餐还没生效 (effect_time: {$packageConfig->effect_time} > 当前时间: " . CUR_DATETIME . ")\n";
    } else {
        echo "   ✅ 套餐已生效\n";
    }
    
    if ($packageConfig->expire_time < CUR_DATETIME) {
        echo "   ❌ 套餐已过期 (expire_time: {$packageConfig->expire_time} < 当前时间: " . CUR_DATETIME . ")\n";
        echo "   这就是导致错误的原因!\n";
    } else {
        echo "   ✅ 套餐未过期\n";
    }
} else {
    echo "   无法进行检查，因为没有找到有效的套餐配置\n";
}

echo "\n";

// 4. 建议解决方案
echo "4. 建议解决方案:\n";
if ($packageConfig && $packageConfig->expire_time < CUR_DATETIME) {
    echo "   问题: 企业套餐已过期，但仍在尝试编辑并发布职位\n";
    echo "   解决方案:\n";
    echo "   1. 为企业续费或升级套餐\n";
    echo "   2. 修改业务逻辑，允许编辑但不允许发布\n";
    echo "   3. 在前端提示用户套餐已过期\n";
    echo "   4. 检查是否应该自动分配免费套餐\n";
} else {
    echo "   当前套餐状态正常，可能是其他问题导致的错误\n";
}

echo "\n=== 调试完成 ===\n";
